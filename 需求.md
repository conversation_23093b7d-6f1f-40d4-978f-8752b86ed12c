# 项目目标

实现一个AI开发脚手架，让用户免去服务端、数据库、消息队列、前端AI流式响应处理等步骤，可以专注AI业务逻辑开发。

# 关键组件

计算层：使用langgraph，用户的主要工作范围。定义提示词、驱动AI生成输出，定义AI输出类型str或pydantic复杂类型
存储层：langgraph驱动，AI生成输出后，输出类型和数据库SQLModel关联，转换后直接入库。如果是多步骤的计算，每一步都入库。通过自定义的task_id关联流程
中间件：消息队列，当存储变化时，发出消息通知订阅方
api：为前端封装后端
前端：vue+element-plus，用于查看任务历史、启动新任务、订阅消息队列查看任务实时进展、批量启动任务、批量订阅任务进展


# 进一步的说明

1. 为了避免用户奔波于数据库、消息队列、存储、缓存等与llm无关的部分，我们希望llm计算完成后，可以很方便的通过SQLModel将数据存储至数据库，数据库管理尽可能简单，比如使用pydantic定义llm的输出类型，使用SQLModel来定义数据表类型，这两个类型密切关联，是用户开始扩展脚手架的起点，用户定义提示词和类型，来完成quick start

2. 用户会频繁添加计算层的类型和提示词，希望新增类型后，能很方便的维护数据库和消息队列，尽可能使用相似甚至同样的类型来驱动整个框架，避免redis一套类型 存储一套类型 langgraph一套类型 fast api一套类型 前端一套类型，同一个数据各层类型不一是这个脚手架的重要解决问题


3. 技术栈：
- AI: 使用langGraph
- api：fast-api
- 存储：初步的想法是使用 pg+redis消息队列，但不确定supabase或pocket这种方案是否可行，是否能让事情更简单，详见需求

# 任务

1. 存储和消息队列方案至关重要
2. 请完成以上需求分析，详细评估最佳实践脚手架设计，包含代码文件结构设计，核心代码示例等。不需要日志、性能监控等边缘部分，专注我提到的核心组件
