# AI开发脚手架设计方案

## 存储与消息队列方案评估

基于您的需求和搜索结果分析，我推荐使用**PostgreSQL原生LISTEN/NOTIFY机制**作为核心消息传递方案，而非完全依赖Supabase Realtime服务。

### 为什么不是Supabase作为主要方案

虽然Supabase提供了实时通知功能，但实际使用中存在稳定性问题："currently I'm relying on supabase realtime which is very very iffy constant random disconnections and errors" 。对于需要可靠任务进度通知的AI工作流系统，这种不稳定性可能影响用户体验。

Supabase Realtime确实基于PostgreSQL的底层能力："Broadcast from Database, to send messages triggered by changes within the Database itself; Postgres Changes, polling Database for changes" ，但我们可以通过直接使用PostgreSQL的LISTEN/NOTIFY获得更可控的实现。

## 推荐架构设计

### 核心技术栈

- **数据库**: PostgreSQL (使用SQLModel作为ORM)
- **消息队列**: PostgreSQL LISTEN/NOTIFY (避免额外基础设施)
- **计算层**: LangGraph
- **API层**: FastAPI
- **前端**: Vue + Element Plus

### 为何选择PostgreSQL LISTEN/NOTIFY

1. **零额外依赖** - 利用已有数据库的原生功能，无需维护独立消息队列
2. **事务一致性** - 可确保数据库变更与消息发送在同事务中
3. **类型统一** - 消息内容可直接使用SQLModel定义的类型
4. **简化架构** - 避免Redis等额外组件的配置和维护

## 代码结构设计

```
ai-scaffold/
├── core/
│   ├── database.py        # 数据库连接与初始化
│   ├── realtime.py        # LISTEN/NOTIFY封装
│   └── types.py           # 基础类型定义
├── models/
│   ├── __init__.py        # SQLModel定义
│   ├── task.py            # 任务主模型
│   └── step.py            # 步骤模型
├── graph/
│   ├── __init__.py        # LangGraph定义
│   ├── nodes.py           # 节点逻辑
│   └── workflow.py        # 工作流组装
├── api/
│   ├── __init__.py
│   ├── tasks.py           # 任务API
│   └── realtime.py        # 实时消息API
├── services/
│   ├── storage.py         # 存储服务
│   └── messaging.py       # 消息服务
└── main.py                # 应用入口
```

## 核心代码示例

### 1. 统一类型定义 (models/\_\_init\_\_.py)

```python
from sqlmodel import SQLModel, Field
from pydantic import BaseModel
from typing import Generic, TypeVar, Optional

T = TypeVar('T')

class TaskBase(SQLModel):
    """所有任务共享的基础字段"""
    task_id: str = Field(primary_key=True, index=True)
    status: str = Field(default="pending")
    created_at: datetime = Field(default_factory=datetime.utcnow)

class StepOutput(BaseModel, Generic[T]):
    """LangGraph节点输出通用结构"""
    task_id: str
    step_name: str
    result: T
    timestamp: datetime = Field(default_factory=datetime.utcnow)

# 具体业务类型示例
class SummaryResult(BaseModel):
    summary: str
    keywords: list[str]
    
SummaryStepOutput = StepOutput[SummaryResult]
```

### 2. 存储服务 (services/storage.py)

```python
from core.database import create_db_session
from core.realtime import publish_event
from sqlmodel import select
from typing import Type, Any

def save_step_result(step_output: StepOutput[Any], model_type: Type[SQLModel]):
    """
    保存步骤结果并触发实时通知
    统一处理SQLModel转换和消息发布
    """
    with create_db_session() as session:
        # 将StepOutput转换为SQLModel实例
        db_instance = model_type(**step_output.dict())
        
        session.add(db_instance)
        session.commit()
        session.refresh(db_instance)
        
        # 事务提交后发布事件
        publish_event(
            channel=f"task:{step_output.task_id}",
            payload={
                "event": "step_completed",
                "step": step_output.step_name,
                "data": step_output.dict()
            }
        )
        
        return db_instance
```

### 3. 实时消息封装 (core/realtime.py)

```python
import asyncpg
from core.config import settings
from typing import Callable, Any

class RealtimeService:
    def __init__(self):
        self.connection = None
        self.listeners = {}
    
    async def connect(self):
        self.connection = await asyncpg.connect(
            settings.DATABASE_URL
        )
    
    async def listen(self, channel: str, callback: Callable[[str, Any], None]):
        """监听特定频道的消息"""
        if channel not in self.listeners:
            await self.connection.add_listener(channel, lambda conn, pid, ch, payload: callback(ch, payload))
            self.listeners[channel] = []
        self.listeners[channel].append(callback)
    
    async def publish_event(self, channel: str, payload: Any):
        """在事务提交后发布事件"""
        await self.connection.execute(
            "SELECT pg_notify($1, $2)", 
            channel, 
            json.dumps(payload)
        )

# 全局实例
realtime = RealtimeService()
```

### 4. LangGraph节点示例 (graph/nodes.py)

```python
from langgraph.graph import StateGraph, END
from models import SummaryStepOutput
from services import storage
from typing import TypedDict

class AgentState(TypedDict):
    task_id: str
    input_text: str
    summary: Optional[str]

async def generate_summary(state: AgentState) -> dict:
    """LangGraph节点：生成摘要"""
    # AI处理逻辑...
    result = await llm_generate_summary(state["input_text"])
    
    # 使用统一类型
    step_output = SummaryStepOutput(
        task_id=state["task_id"],
        step_name="summary",
        result=result
    )
    
    # 自动存储并触发通知
    storage.save_step_result(step_output, SummaryResultDB)
    
    return {"summary": result.summary}

# 注意：这里返回的类型与SQLModel定义直接关联
# 用户只需定义SummaryResultDB和SummaryStepOutput，框架自动处理存储和通知
```

### 5. API实时端点 (api/realtime.py)

```python
from fastapi import APIRouter, WebSocket, WebSocketDisconnect
from core.realtime import realtime

router = APIRouter()

@router.websocket("/ws/tasks/{task_id}")
async def task_progress(websocket: WebSocket, task_id: str):
    await websocket.accept()
    
    async def handle_message(channel: str, payload: str):
        await websocket.send_text(payload)
    
    try:
        # 订阅特定任务频道
        await realtime.listen(f"task:{task_id}", handle_message)
        
        # 发送历史状态
        # ...查询数据库中的历史步骤
        
        while True:
            # 保持连接
            await asyncio.sleep(3600)
            
    except WebSocketDisconnect:
        await realtime.remove_listener(f"task:{task_id}", handle_message)
```

## 类型统一实现关键

1. **单一类型源**：用户只需定义一次Pydantic模型（如`SummaryResult`）
   - LangGraph节点使用`StepOutput[SummaryResult]`作为返回类型
   - SQLModel继承自同一基础类型创建数据库模型
   - API响应直接使用相同类型
   - 消息队列传递序列化后的相同结构

2. **自动转换机制**：
   ```python
   # 用户定义
   class SummaryResult(BaseModel):
       summary: str
       keywords: list[str]
   
   # 框架自动处理
   class SummaryResultDB(SQLModel, table=True):
       task_id: str = Field(primary_key=True)
       step_name: str
       summary: str
       keywords: str  # JSON存储或分开字段
       timestamp: datetime
   ```

3. **事务安全的消息发布**：在数据库事务提交后才发送消息，确保"存储变化时发出消息"的可靠性

## 部署优势

这种设计实现了您要求的"避免redis一套类型 存储一套类型 langgraph一套类型 fast api一套类型 前端一套类型"的问题。通过SQLModel作为核心类型枢纽，整个系统保持类型一致性，用户只需关注AI业务逻辑和提示词设计。

当用户添加新的计算类型时，只需：
1. 定义Pydantic输出模型
2. 创建对应的SQLModel数据库模型
3. 编写LangGraph节点函数

框架会自动处理存储、消息通知和API端点，真正实现"专注AI业务逻辑开发"的目标。
